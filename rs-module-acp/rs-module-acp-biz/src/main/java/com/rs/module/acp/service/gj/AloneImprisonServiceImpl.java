package com.rs.module.acp.service.gj;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.exception.ServiceException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.aloneimprison.AloneImprisonApproveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.aloneimprison.AloneImprisonListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.aloneimprison.AloneImprisonPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.aloneimprison.AloneImprisonSaveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsACPSaveVO;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsSaveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.prisonroom.PrisonRoomChangAutoRecordVO;
import com.rs.module.acp.dao.gj.AloneImprisonDao;
import com.rs.module.acp.entity.gj.AloneImprisonDO;
import com.rs.module.acp.service.gj.prisonroom.PrisonRoomChangeService;
import com.rs.module.acp.service.gj.punishment.PunishmentService;
import com.rs.module.acp.util.AloneImprisonMsgSendUtil;
import com.rs.module.acp.util.GjBusTraceUtil;
import com.rs.module.acp.util.PunishmentUtil;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;
import com.rs.module.base.enums.BusTypeEnum;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.service.sys.BusTraceService;
import com.rs.module.base.util.BspApprovalUtil;
import com.rs.util.DateUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 实战平台-管教业务-单独关押登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AloneImprisonServiceImpl extends BaseServiceImpl<AloneImprisonDao, AloneImprisonDO> implements AloneImprisonService {

    @Resource
    private AloneImprisonDao aloneImprisonDao;
    @Resource
    private PrisonerService prisonerService;

    @Resource
    private InOutRecordsService inOutRecordsService;

    @Resource
    private PunishmentService punishmentService;
    @Resource
    private PrisonRoomChangeService prisonRoomChangeService;

    @Resource
    private BusTraceService  busTraceService;
    @Override
    public String createAloneImprison(AloneImprisonSaveReqVO createReqVO) {
        // 插入
        AloneImprisonDO aloneImprison = BeanUtils.toBean(createReqVO, AloneImprisonDO.class);
        aloneImprisonDao.insert(aloneImprison);
        // 返回
        return aloneImprison.getId();
    }

    @Override
    public void updateAloneImprison(AloneImprisonSaveReqVO updateReqVO) {
        // 校验存在
        validateAloneImprisonExists(updateReqVO.getId());
        // 更新
        AloneImprisonDO updateObj = BeanUtils.toBean(updateReqVO, AloneImprisonDO.class);
        aloneImprisonDao.updateById(updateObj);
    }

    @Override
    public void deleteAloneImprison(String id) {
        // 校验存在
        validateAloneImprisonExists(id);
        // 删除
        aloneImprisonDao.deleteById(id);
    }

    private void validateAloneImprisonExists(String id) {
        if (aloneImprisonDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-单独关押登记数据不存在");
        }
    }

    @Override
    public AloneImprisonDO getAloneImprison(String id) {
        return aloneImprisonDao.selectById(id);
    }

    @Override
    public PageResult<AloneImprisonDO> getAloneImprisonPage(AloneImprisonPageReqVO pageReqVO) {
        return aloneImprisonDao.selectPage(pageReqVO);
    }

    @Override
    public List<AloneImprisonDO> getAloneImprisonList(AloneImprisonListReqVO listReqVO) {
        return aloneImprisonDao.selectList(listReqVO);
    }

    @Override
    public List<AreaPrisonRoomDO> getAloneImprisonRoomList(String nowRoomId) {
        String orgCode = SessionUserUtil.getSessionUser().getOrgCode();
        return aloneImprisonDao.getAloneImprisonRoomList(nowRoomId,orgCode);
    }
    private final static String FLOW_NAME = "danduguanya";
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String save(AloneImprisonSaveReqVO createReqVO) throws Exception {
        // 插入
        AloneImprisonDO aloneImprison = BeanUtils.toBean(createReqVO, AloneImprisonDO.class);
        try {
            validExist(aloneImprison);
        }catch (Exception e){
            throw new ServerException(e.getMessage());
        }
        if(aloneImprison.getIsAssociatedPunishment() == null) aloneImprison.setIsAssociatedPunishment(0);
        aloneImprison.setStatus("01");
        Map<String, Object> variables = new HashMap<>();
        String msgTit = "";
        String msgUrl = "";
        aloneImprison.setId(StringUtil.getGuid());
        //启动流程
        Map<String,String> processResult = BspApprovalUtil.commonStartProcessMap(FLOW_NAME, aloneImprison.getId(), msgTit, msgUrl, variables, HttpUtils.getAppCode());
        if(StringUtil.isEmpty(processResult.get("actInstId")) || StringUtil.isEmpty(processResult.get("taskId"))){
            throw new Exception("启动流程失败");
        }
        aloneImprison.setActInstId(processResult.get("actInstId"));
        aloneImprison.setTaskId(processResult.get("taskId"));
        aloneImprisonDao.insert(aloneImprison);
        // 返回
        return aloneImprison.getId();
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean approve(AloneImprisonApproveReqVO approveReqVO) throws Exception{
        //从当前登录用户信息设置审批信息默认值
        if(StringUtil.isEmpty(approveReqVO.getApproverXm())) approveReqVO.setApproverXm(SessionUserUtil.getSessionUser().getName());
        if(StringUtil.isEmpty(approveReqVO.getApproverSfzh())) approveReqVO.setApproverSfzh(SessionUserUtil.getSessionUser().getIdCard());
        if(approveReqVO.getApproverTime() == null) approveReqVO.setApproverTime(new Date());
        AloneImprisonDO aloneImprisonDO = aloneImprisonDao.selectById(approveReqVO.getId());
        BspApproceStatusEnum isApprove = BspApproceStatusEnum.NOT_PASSED;
        String status = "03";
        if(approveReqVO.getApprovalResult().equals("1")){//審批同意 ZD_DDGY_SPZT
            status = "02";
            isApprove = BspApproceStatusEnum.PASSED;
            try {
                validExist(aloneImprisonDO);
            }catch (Exception e){
                throw new Exception(e.getMessage());
            }
        }
        approveReqVO.setActInstId(aloneImprisonDO.getActInstId());
        approveReqVO.setTaskId(aloneImprisonDO.getTaskId());
        approveReqVO.setDefKey(FLOW_NAME);
        BeanUtil.copyProperties( approveReqVO,aloneImprisonDO);
        aloneImprisonDO.setStartTime(new Date());
        aloneImprisonDO.setStatus(status);
        //调用流程审批接口
        String msgTit = "";
        String msgUrl = "";
        Map<String, Object> variables = new HashMap<>();
        variables.put("busType",  "10");//ZD_MSG_BUSTYPE 字典对应code
        Map<String,String> approvalResult = BspApprovalUtil.approvalProcessMap(approveReqVO,
                isApprove,
                msgTit,
                msgUrl,
                true,
                null,
                null,
                HttpUtils.getAppCode()
        );
        String taskId = approvalResult.get("taskId"); // 更新任务ID
        aloneImprisonDO.setTaskId(taskId);
        aloneImprisonDao.updateById(aloneImprisonDO);
        if(!approveReqVO.getApprovalResult().equals("1")) return true;
        PrisonerVwRespVO vwRespVO = prisonerService.getPrisonerByJgrybm(aloneImprisonDO.getJgrybm());

        //审批成功发送业务消息
        AloneImprisonMsgSendUtil.sendMsg(aloneImprisonDO,vwRespVO);
        //业务日志写入
        busTraceService.saveBusTrace(BusTypeEnum.YEWU_DDGY,GjBusTraceUtil.buildAloneImprisonBusTraceContent(aloneImprisonDO,vwRespVO.getXm()),
                aloneImprisonDO.getJgrybm(),
                SessionUserUtil.getSessionUser().getOrgCode(),
                aloneImprisonDO.getId());
        //inOutRecordsService.initOutRecords(aloneImprisonDO.getJgrybm(),  aloneImprisonDO.getOldRoomId(),"08", aloneImprisonDO.getId(),"0802");
        //是否关联惩罚
        if(1 == aloneImprisonDO.getIsAssociatedPunishment()) {
            punishmentService.saveExternal(PunishmentUtil.convertPunishmentVO(aloneImprisonDO,vwRespVO.getXm()));
        }
        PrisonRoomChangAutoRecordVO prisonRoomChangAutoRecordVO = new PrisonRoomChangAutoRecordVO();
        prisonRoomChangAutoRecordVO.setJgrybm(aloneImprisonDO.getJgrybm());
        prisonRoomChangAutoRecordVO.setOldRoomId(aloneImprisonDO.getOldRoomId());
        prisonRoomChangAutoRecordVO.setNewRoomId(aloneImprisonDO.getNewRoomId());
        prisonRoomChangAutoRecordVO.setOldRoomName(aloneImprisonDO.getOldRoomName());
        prisonRoomChangAutoRecordVO.setNewRoomName(aloneImprisonDO.getNewRoomName());
        prisonRoomChangAutoRecordVO.setChangeReason("单独关押");
        prisonRoomChangAutoRecordVO.setBusinessType("0802");
        prisonRoomChangAutoRecordVO.setRoomChangeTime(aloneImprisonDO.getApproverTime());
        prisonRoomChangAutoRecordVO.setSourceBusinessId(aloneImprisonDO.getId());

        //写入监室待调整记录
        prisonRoomChangeService.automaticGenerationRecord(prisonRoomChangAutoRecordVO);
        return true;
    }

    @Override
    public boolean saveInOutRecords(InOutRecordsACPSaveVO saveReqVO) {
        InOutRecordsSaveReqVO inOutRecordsSaveReqVO = BeanUtils.toBean(saveReqVO, InOutRecordsSaveReqVO.class);
        AloneImprisonDO aloneImprisonDO = getAloneImprison(saveReqVO.getId());
        if(aloneImprisonDO == null){
            throw new ServiceException("请传入正确的参数！");
        }
        if("04".equals(aloneImprisonDO.getStatus())){
            throw new ServiceException("已监室调整完成！");
        }
        inOutRecordsSaveReqVO.setId("");
        inOutRecordsSaveReqVO.setBusinessId(saveReqVO.getId());
        inOutRecordsSaveReqVO.setJgrybm(aloneImprisonDO.getJgrybm());
        PrisonerVwRespVO vwRespVO = prisonerService.getPrisonerByJgrybm(aloneImprisonDO.getJgrybm());
        inOutRecordsSaveReqVO.setJgryxm(vwRespVO.getXm());
        inOutRecordsSaveReqVO.setRoomId(aloneImprisonDO.getOldRoomId());
        inOutRecordsSaveReqVO.setBusinessType("08");
        inOutRecordsSaveReqVO.setBusinessSubType("0801");
        inOutRecordsService.acpSaveInOutRecords(inOutRecordsSaveReqVO,aloneImprisonDO.getNewRoomId(),saveReqVO.getInTime());
        aloneImprisonDO.setStatus("04");//更新状态为监室调整完成
        aloneImprisonDO.setStartTime(saveReqVO.getInTime());//更新单独关押开始时间为出入记录的入监时间
        aloneImprisonDO.setOutTime(saveReqVO.getInoutTime());
        aloneImprisonDao.updateById(aloneImprisonDO);

        // 同步数据到监室调整
        prisonRoomChangeService.saveInOutRecords(saveReqVO);
        return false;
    }
    //查询当前时间在单独关押期间的人员
    @Override
    public List<AloneImprisonDO> getAloneImprisonListNow() {
        return aloneImprisonDao.selectList(new LambdaQueryWrapper<AloneImprisonDO>()
                .in(AloneImprisonDO::getStatus, new String[]{"02","04"})
                .eq(AloneImprisonDO::getIsDel, 0)
                .le(AloneImprisonDO::getStartTime, DateUtil.getStartDateTime())
                .ge(AloneImprisonDO::getEndTime, DateUtil.getEndDateTime()));
    }
    //查询当前人员是否已经单独关押
    @Override
    public boolean isExist(String jgrybm) {
        return aloneImprisonDao.selectCount(new LambdaQueryWrapper<AloneImprisonDO>()
                .eq(AloneImprisonDO::getJgrybm, jgrybm)
                .in(AloneImprisonDO::getStatus, new String[]{"02","04"})) > 0;
    }
    @Override
    public AloneImprisonDO rootExistPrison(String roomId) {
        return aloneImprisonDao.selectOne(new LambdaQueryWrapper<AloneImprisonDO>()
                .eq(AloneImprisonDO::getNewRoomId, roomId)
                .in(AloneImprisonDO::getStatus, new String[]{"02","04"}).orderByDesc(AloneImprisonDO::getAddTime));
    }
    public boolean validExist(AloneImprisonDO aloneImprisonDO) throws Exception {
        //判断当前人员是否已经单独关押
        if(isExist(aloneImprisonDO.getJgrybm())){
            throw new Exception("当前人员已单独关押！");
        }
        //判断当前监室是否已关押人员
        AloneImprisonDO entity = rootExistPrison(aloneImprisonDO.getNewRoomId());
        if(null != entity && StringUtil.isNotEmpty(entity.getId())){
            throw new Exception("当前监室已存在关押人员！");
        }
        return false;
    }
    @Override
    public void roomChangeAloneImprison(String jgrybm){
        AloneImprisonDO aloneImprisonDO = aloneImprisonDao.selectOne(new LambdaQueryWrapper<AloneImprisonDO>()
                .eq(AloneImprisonDO::getJgrybm, jgrybm)
                .eq(AloneImprisonDO::getIsDel, 0)
                .eq(AloneImprisonDO::getStatus, "04"));
        if(aloneImprisonDO == null) return;
        aloneImprisonDO.setStatus("04");
        aloneImprisonDao.updateById(aloneImprisonDO);
    }
    /**
     * 指定人员最新单独关押状态更改为已完成 05 ，关押结束时间更新
     * @param jgrybm
     */
    @Override
    public void finishAloneImprison(String jgrybm,Date endTime){
        AloneImprisonDO aloneImprisonDO = aloneImprisonDao.selectOne(new LambdaQueryWrapper<AloneImprisonDO>()
                .eq(AloneImprisonDO::getJgrybm, jgrybm)
                .eq(AloneImprisonDO::getIsDel, 0)
                .eq(AloneImprisonDO::getStatus, "04"));
        if(aloneImprisonDO == null) return;
        aloneImprisonDO.setStatus("05");
        if(endTime == null) endTime = new Date();
        aloneImprisonDO.setEndTime(endTime);
        aloneImprisonDao.updateById(aloneImprisonDO);
        PrisonerVwRespVO vwRespVO = prisonerService.getPrisonerByJgrybm(aloneImprisonDO.getJgrybm());
        //更新日志轨迹 结束时间
        busTraceService.updateBusTraceByBusinessId(aloneImprisonDO.getId(), jgrybm, BusTypeEnum.YEWU_DDGY,
                GjBusTraceUtil.buildAloneImprisonBusTraceContent(aloneImprisonDO, vwRespVO.getXm()));
        if(1 == aloneImprisonDO.getIsAssociatedPunishment()) {
            //根据业务ID 更新惩罚结束时间
            //punishmentService.updatePunishmentEndTimeByBusinessId(aloneImprisonDO.getId(), aloneImprisonDO.getEndTime());
        }
    }
}
