package com.rs.module.acp.service.pm;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.HttpUtil;
import com.bsp.common.util.StringUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.dao.pm.UserDao;
import com.rs.module.acp.entity.pm.UserDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;


/**
 * 实战平台-监管管理-用户 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Validated
public class UserServiceImpl extends BaseServiceImpl<UserDao, UserDO> implements UserService {

    @Resource
    private UserDao userDao;
    @Value("${conf.server.ip:127.0.0.1}")
    private String bspServerIp;
    @Override
    public String createUser(UserSaveReqVO createReqVO) {
        // 插入
        UserDO user = BeanUtils.toBean(createReqVO, UserDO.class);
        userDao.insert(user);
        // 返回
        return user.getId();
    }

    @Override
    public void updateUser(UserSaveReqVO updateReqVO) {
        // 校验存在
        validateUserExists(updateReqVO.getId());
        // 更新
        UserDO updateObj = BeanUtils.toBean(updateReqVO, UserDO.class);
        userDao.updateById(updateObj);
    }

    @Override
    public void deleteUser(String id) {
        // 校验存在
        validateUserExists(id);
        // 删除
        userDao.deleteById(id);
    }

    private void validateUserExists(String id) {
        if (userDao.selectById(id) == null) {
            throw new ServerException("实战平台-监管管理-用户数据不存在");
        }
    }

    @Override
    public UserDO getUser(String id) {
        return userDao.selectById(id);
    }

    @Override
    public String mjSave(UserMJSaveReqVO reqVO, HttpServletRequest request) throws Exception{
        UserDO entity = BeanUtils.toBean(reqVO, UserDO.class);
        entity.setRylx("01");
        return commonSaveOrUpdate(entity,request);
    }

    @Override
    public String fjSave(UserFJSaveReqVO reqVO, HttpServletRequest request) throws Exception{
        UserDO entity = BeanUtils.toBean(reqVO, UserDO.class);
        entity.setRylx("02");
        return commonSaveOrUpdate(entity,request);
    }

    @Override
    public String ywSave(UserYWSaveReqVO reqVO, HttpServletRequest request) throws Exception{
        UserDO entity = BeanUtils.toBean(reqVO, UserDO.class);
        entity.setRylx("03");
        return commonSaveOrUpdate(entity,request);
    }

    @Override
    public String gqSave(UserGQSaveReqVO reqVO, HttpServletRequest request) throws Exception{
        UserDO entity = BeanUtils.toBean(reqVO, UserDO.class);
        entity.setRylx("04");
        return commonSaveOrUpdate(entity,request);
    }
    private UserDO validateReqUser(UserDO entity) throws  Exception {
        UserDO result = baseMapper.selectOne(new LambdaQueryWrapper<UserDO>().eq(UserDO::getIdCard,entity.getIdCard()).eq(UserDO::getIsDisabled,"0"));
        if(result != null && StringUtil.isNotEmpty(result.getId())){
            throw new Exception(String.format("当前用户身份证号：%s数据已存在！", entity.getIdCard()));
        }
        return result;
    }
    public String commonSaveOrUpdate(UserDO entity, HttpServletRequest request) throws Exception{
        String id = entity.getId();
        if(StringUtil.isEmpty(entity.getIsDisabled()))entity.setIsDisabled("0");
        if("01".equals(entity.getRylx())){
            entity.setIsFj("0");
        }else{
            entity.setIsFj("1");
        }
        try{
            //同步到BSP 用户
            if(!"04".equals(entity.getRylx())){ //工勤人员不同步到bsp
                syncToBspUser(entity,request);
            }
        }catch (Exception e){
            throw new Exception(e.getMessage());
        }
        if(StringUtil.isNotEmpty(id)){
            updateById(entity);
        }else{
            if(validateReqUser(entity) != null){
                throw new Exception("实战平台-监管管理-用户数据已存在");
            }
            id = StringUtil.getGuid();
            entity.setId(id);
            save(entity);
        }
        return id;
    }
    @Override
    public void syncToBspUser(UserDO entity, HttpServletRequest request) throws Exception{
        commonSyncToBspUserBatch(Collections.singletonList(entity),request);
    }

    /**
     * 从bsp 同步用户信息 全量
     * @param orgId
     * @param request
     */
    @Override
    public String syncFromToBspUser(String orgId, HttpServletRequest request) throws  Exception{
        //bspServerIp = "127.0.0.1";
        String reqUrl = String.format("http://%s:1910/api/v1/userorg/user/getUserByPage", bspServerIp);
        Map<String, Object> paramMap = new HashMap<>();
        if(StringUtil.isNotEmpty(orgId)) paramMap.put("orgId", orgId);
        paramMap.put("pageNo", 1);
        paramMap.put("pageSize", 10000);
        String access_token = request.getParameter("access_token");
        paramMap.put("access_token", access_token);
        String resultStr = HttpUtil.post(reqUrl, paramMap);
        JSONObject result = JSONObject.parseObject(resultStr);
        if (result.getInteger("code") != 200) throw new Exception("查询BSP用户信息失败!");
        List<JSONObject> list = result.getJSONArray("data").toJavaList(JSONObject.class);
        List<UserDO> userDOList = new ArrayList<>();
        for (JSONObject jsonObject : list) {
            UserDO entity = BeanUtils.toBean(jsonObject, UserDO.class);
            userDOList.add( entity);
        }
        userDao.deleteBSPUser(orgId);
        log.info("acp deleteBSPUser success");
        userDao.insertBatch(userDOList);
        log.info("acp insertBatch success");
        return "success";
    }
    @Override
    public void syncToBspUserBatch(String userIds, HttpServletRequest request) throws Exception{
        List<UserDO> list = userDao.selectList(new LambdaQueryWrapper<UserDO>().in(UserDO::getId,userIds.split(",")));
        commonSyncToBspUserBatch(list,request);
    }

    @Override
    public void deleteUserGq(String id) {
        UserDO entity = userDao.selectById(id);
        if (entity== null) {
            throw new ServerException("实战平台-监管管理-用户数据不存在");
        }
        if(!"04".equals(entity.getRylx())){
            throw new ServerException("实战平台-监管管理-当前用户非工勤人员不允许删除");
        }
        // 删除
        userDao.deleteById(id);
    }

    private void commonSyncToBspUserBatch(List<UserDO> list,HttpServletRequest request) throws Exception{
        //bspServerIp = "127.0.0.1";
        String reqUrl = String.format("http://%s:1910/openapi/v1/init/saveOrUpdateUsers", bspServerIp);
        String requestBody = JSONObject.toJSONString(Collections.singletonList(list));

        HttpResponse response = HttpRequest.post(reqUrl)
                .header("Content-Type", "application/json")
                //.header("Authorization", "Bearer " + bspToken)  // 如果你的接口通过 Authorization 头验证
                .form("access_token", request.getParameter("access_token"))               // 或者通过参数传递
                .body(requestBody)
                .execute();
        String resultStr = response.body();
        JSONObject result = JSONObject.parseObject(resultStr);
        if (result.getInteger("code") != 200) {
            throw new Exception("同步到BSP用户信息失败!");
        }
    }
}
