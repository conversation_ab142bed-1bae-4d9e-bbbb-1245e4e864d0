package com.rs.module.ptm.service;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.ptm.domain.entity.GoodsOutDO;
import com.rs.module.ptm.domain.vo.*;

import javax.validation.Valid;
import java.util.List;


/**
 * 附物管理-附物取出登记 Service 接口
 *
 * <AUTHOR>
 */
public interface GoodsOutService extends IBaseService<GoodsOutDO> {

    /**
     * 创建附物管理-附物取出登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createGoodsOut(@Valid GoodsOutSaveReqVO createReqVO);

    /**
     * 更新附物管理-附物取出登记
     *
     * @param updateReqVO 更新信息
     */
    void updateGoodsOut(@Valid GoodsOutSaveReqVO updateReqVO);

    /**
     * 删除附物管理-附物取出登记
     *
     * @param id 编号
     */
    void deleteGoodsOut(String id);

    /**
     * 获得附物管理-附物取出登记
     *
     * @param id 编号
     * @return 附物管理-附物取出登记
     */
    GoodsOutDO getGoodsOut(String id);

    /**
    * 获得附物管理-附物取出登记分页
    *
    * @param pageReqVO 分页查询
    * @return 附物管理-附物取出登记分页
    */
    PageResult<GoodsOutDO> getGoodsOutPage(GoodsOutPageReqVO pageReqVO);

    /**
    * 获得附物管理-附物取出登记列表
    *
    * @param listReqVO 查询条件
    * @return 附物管理-附物取出登记列表
    */
    List<GoodsOutDO> getGoodsOutList(GoodsOutListReqVO listReqVO);


    List<GoodsOutRespVO> getDqwByJgrybm(String jgrybm);

    GoodsOutRespVO getDetailsById(String id);

    boolean takeOutGoodsApproval(GoodsOutApprovalVO approvalVO);

    String takeOutGoods(String id);

    PageResult<GoodsOutApprovalRespVO> takeOutGoodsApprovalPage(GoodsOutApprovalPageReqVO goodsOutApprovalPageReqVO);
}
