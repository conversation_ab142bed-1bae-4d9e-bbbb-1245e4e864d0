package com.rs.module.ptm.domain.vo;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@ApiModel(description = "管理后台 - 附物管理-附物取出登记明细分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class GoodsOutDetailPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("附物取出ID")
    private String personalGoodsOutId;

    @ApiModelProperty("物品编号")
    private String wpbh;

    @ApiModelProperty("取出数量")
    private Integer takeoutQuantity;

    @ApiModelProperty("备注")
    private String remark;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
