package com.rs.module.ptm.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 附物管理-附物 DO
 *
 * <AUTHOR>
 */
@TableName("ptm_personal_goods")
@KeySequence("ptm_personal_goods_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ptm_personal_goods")
public class GoodsDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 状态
     */
    private String status;
    /**
     * 登记经办人
     */
    private String operatorSfzh;
    /**
     * 登记经办人姓名
     */
    private String operatorXm;
    /**
     * 登记经办时间
     */
    private Date operatorTime;

    /**
     * 存放时间
     */
    private Date storageTime;

    /**
     * 存放人身份证号
     */
    private String storageSfzh;

    /**
     * 存放人姓名
     */
    private String storageXm;




}
