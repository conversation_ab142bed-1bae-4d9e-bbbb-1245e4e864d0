package com.rs.module.ptm.domain.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 附物管理-附物存入取出列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class GoodsInOutListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("存入取出类型，（01：存入，02：取出）")
    private String inOutType;

    @ApiModelProperty("业务ID（存入存附物主表ID,取出存附物取出主表ID）")
    private String businessId;

    @ApiModelProperty("物品编号")
    private String wpbh;

    @ApiModelProperty("物品名称")
    private String name;

    @ApiModelProperty("物品类型")
    private String goodsType;

    @ApiModelProperty("数量")
    private Integer quantity;

    @ApiModelProperty("数量单位")
    private String unit;

    @ApiModelProperty("存放位置，如A监区主柜B09号柜")
    private String storageLocation;

    @ApiModelProperty("物品特征描述")
    private String features;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("取出数量")
    private Integer takeoutQuantity;

    @ApiModelProperty("取出原因")
    private String takeoutReason;

    @ApiModelProperty("取出方式")
    private String takeoutMethod;

    @ApiModelProperty("登记经办人")
    private String operatorSfzh;

    @ApiModelProperty("登记经办人姓名")
    private String operatorXm;

    @ApiModelProperty("登记经办时间")
    private Date[] operatorTime;

}
