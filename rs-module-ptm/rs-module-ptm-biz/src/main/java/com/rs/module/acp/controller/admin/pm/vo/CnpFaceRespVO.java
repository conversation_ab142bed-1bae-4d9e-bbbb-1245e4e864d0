package com.rs.module.acp.controller.admin.pm.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 实战平台-监管管理-仓内外屏人脸信息维护 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CnpFaceRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("人员类型 1警员、2在押人员")
    private Short personnelType;
    @ApiModelProperty("人员编号、警员id")
    private String personnelId;
    @ApiModelProperty("照片")
    private String photo;
}
