package com.rs.module.acp.controller.admin.pm.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-监管管理-仓内外屏人脸信息维护新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CnpFaceSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("人员类型 1警员、2在押人员")
    private Short personnelType;

    @ApiModelProperty("人员编号、警员id")
    private String personnelId;

    @ApiModelProperty("照片")
    private String photo;

}
