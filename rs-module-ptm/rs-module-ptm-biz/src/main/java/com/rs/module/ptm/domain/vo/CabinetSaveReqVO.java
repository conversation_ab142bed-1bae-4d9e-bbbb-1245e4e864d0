package com.rs.module.ptm.domain.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@ApiModel(description = "管理后台 - 附物管理-储物柜子信息新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CabinetSaveReqVO extends BaseVO{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("柜子名称")
    @NotEmpty(message = "柜子名称不能为空")
    private String name;

    //@ApiModelProperty("柜子编号，如A-01、B-12（区域+序号）")
    //@NotEmpty(message = "柜子编号，如A-01、B-12（区域+序号）不能为空")
    //private String code;

    @ApiModelProperty("柜子型号ID")
    //@NotEmpty(message = "柜子类型ID")
    private String typeId;

    //@ApiModelProperty("柜子实际位置，如1楼储物间A区")
    //private String cabinetLocation;

    @ApiModelProperty("所属区域ID")
    @NotEmpty(message = "所属区域ID不能为空")
    private String areaId;

    @ApiModelProperty("所属区域名称")
    @NotEmpty(message = "所属区域名称不能为空")
    private String areaName;

    @ApiModelProperty("柜子IP")
    @NotEmpty(message = "柜子IP不能为空")
    private String ip;

    @ApiModelProperty("柜子端口")
    @NotNull(message = "柜子端口不能为空")
    private Integer port;

    @ApiModelProperty("柜子mac地址")
    @NotEmpty(message = "柜子mac地址不能为空")
    private String mac;

    //@ApiModelProperty("柜子总格子数，可动态配置（如20格、30格）")
    //@NotNull(message = "柜子总格子数，可动态配置（如20格、30格）不能为空")
    //private Integer totalGrids;
    //
    //@ApiModelProperty("柜子状态：1-可用/2-维修中/3-停用（枚举值）")
    //@NotNull(message = "柜子状态：1-可用/2-维修中/3-停用（枚举值）不能为空")
    //private Short status;

    @ApiModelProperty("是否主柜")
    @NotNull(message = "是否主柜不能为空")
    private Integer isMain;

    @ApiModelProperty("主柜子ID")
    private String mainCabinetId;

    @ApiModelProperty("柜子排序编号")
    private Integer cabinetSort;

    @ApiModelProperty("版本号")
    private String copyright;

    @ApiModelProperty("开柜方式 1刷脸，2账号 3手势： 字典：ZD_FWGJ_KGFS")
    private String cabinetOpenMethod;

    @ApiModelProperty("操作时间-秒")
    private Integer operationTime;

    @ApiModelProperty("相机设置 旋转角度-秒")
    private Integer cameraSetting;

    @ApiModelProperty("版本号")
    private String versionNumber;

}
