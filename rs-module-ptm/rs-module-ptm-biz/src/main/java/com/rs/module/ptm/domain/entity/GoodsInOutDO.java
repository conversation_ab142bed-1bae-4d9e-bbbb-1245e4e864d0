package com.rs.module.ptm.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 附物管理-附物存入取出 DO
 *
 * <AUTHOR>
 */
@TableName("ptm_personal_goods_in_out")
@KeySequence("ptm_personal_goods_in_out_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ptm_personal_goods_in_out")
public class GoodsInOutDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 存入取出类型，（01：存入，02：取出）
     */
    private String inOutType;
    /**
     * 业务ID（存入存附物主表ID,取出存附物取出主表ID）
     */
    private String businessId;
    /**
     * 物品编号
     */
    private String wpbh;
    /**
     * 物品名称
     */
    private String name;
    /**
     * 物品类型
     */
    private String goodsType;
    /**
     * 数量
     */
    private Integer quantity;
    /**
     * 数量单位
     */
    private String unit;
    /**
     * 存放位置，如A监区主柜B09号柜
     */
    private String storageLocation;
    /**
     * 物品特征描述
     */
    private String features;
    /**
     * 备注
     */
    private String remark;
    /**
     * 状态
     */
    private String status;
    /**
     * 取出数量
     */
    private Integer takeoutQuantity;
    /**
     * 取出原因
     */
    private String takeoutReason;
    /**
     * 取出方式
     */
    private String takeoutMethod;
    /**
     * 登记经办人
     */
    private String operatorSfzh;
    /**
     * 登记经办人姓名
     */
    private String operatorXm;
    /**
     * 登记经办时间
     */
    private Date operatorTime;

}
