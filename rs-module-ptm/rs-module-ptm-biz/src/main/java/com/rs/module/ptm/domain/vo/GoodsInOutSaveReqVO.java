package com.rs.module.ptm.domain.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 附物管理-附物存入取出新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class GoodsInOutSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("存入取出类型，（01：存入，02：取出）")
    @NotEmpty(message = "存入取出类型，（01：存入，02：取出）不能为空")
    private String inOutType;

    @ApiModelProperty("业务ID（存入存附物主表ID,取出存附物取出主表ID）")
    @NotEmpty(message = "业务ID（存入存附物主表ID,取出存附物取出主表ID）不能为空")
    private String businessId;

    @ApiModelProperty("物品编号")
    @NotEmpty(message = "物品编号不能为空")
    private String wpbh;

    @ApiModelProperty("物品名称")
    @NotEmpty(message = "物品名称不能为空")
    private String name;

    @ApiModelProperty("物品类型")
    @NotEmpty(message = "物品类型不能为空")
    private String goodsType;

    @ApiModelProperty("数量")
    @NotNull(message = "数量不能为空")
    private Integer quantity;

    @ApiModelProperty("数量单位")
    @NotEmpty(message = "数量单位不能为空")
    private String unit;

    @ApiModelProperty("存放位置，如A监区主柜B09号柜")
    @NotEmpty(message = "存放位置，如A监区主柜B09号柜不能为空")
    private String storageLocation;

    @ApiModelProperty("物品特征描述")
    @NotEmpty(message = "物品特征描述不能为空")
    private String features;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("状态")
    @NotEmpty(message = "状态不能为空")
    private String status;

    @ApiModelProperty("取出数量")
    private Integer takeoutQuantity;

    @ApiModelProperty("取出原因")
    private String takeoutReason;

    @ApiModelProperty("取出方式")
    private String takeoutMethod;

    @ApiModelProperty("登记经办人")
    @NotEmpty(message = "登记经办人不能为空")
    private String operatorSfzh;

    @ApiModelProperty("登记经办人姓名")
    @NotEmpty(message = "登记经办人姓名不能为空")
    private String operatorXm;

    @ApiModelProperty("登记经办时间")
    @NotNull(message = "登记经办时间不能为空")
    private Date operatorTime;

}
