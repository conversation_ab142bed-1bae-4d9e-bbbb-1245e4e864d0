package com.rs.module.acp.controller.admin.pm.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-监管管理-仓内外屏人脸信息维护分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CnpFacePageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("人员类型 1警员、2在押人员")
    private Short personnelType;

    @ApiModelProperty("人员编号、警员id")
    private String personnelId;

    @ApiModelProperty("照片")
    private String photo;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
