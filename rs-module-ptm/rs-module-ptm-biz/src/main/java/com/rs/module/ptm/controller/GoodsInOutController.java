package com.rs.module.ptm.controller;

import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Api(tags = "附物管理-附物存入取出")
@RestController
@RequestMapping("/ptm/personal/goodsInOut")
@Validated
public class GoodsInOutController {

//    @Resource
//    private GoodsInOutService goodsInOutService;
//
//    @PostMapping("/create")
//    @ApiOperation(value = "创建附物管理-附物存入取出")
//    public CommonResult<String> createGoodsInOut(@Valid @RequestBody GoodsInOutSaveReqVO createReqVO) {
//        return success(goodsInOutService.createGoodsInOut(createReqVO));
//    }
//
//    @PostMapping("/update")
//    @ApiOperation(value = "更新附物管理-附物存入取出")
//    public CommonResult<Boolean> updateGoodsInOut(@Valid @RequestBody GoodsInOutSaveReqVO updateReqVO) {
//        goodsInOutService.updateGoodsInOut(updateReqVO);
//        return success(true);
//    }
//
//    @GetMapping("/delete")
//    @ApiOperation(value = "删除附物管理-附物存入取出")
//    @ApiImplicitParam(name = "ids", value = "编号")
//    public CommonResult<Boolean> deleteGoodsInOut(@RequestParam("ids") String ids) {
//        String[] strings = ids.split(",");
//        for (String id : strings) {
//           goodsInOutService.deleteGoodsInOut(id);
//        }
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @ApiOperation(value = "获得附物管理-附物存入取出")
//    @ApiImplicitParam(name = "id", value = "编号")
//    public CommonResult<GoodsInOutRespVO> getGoodsInOut(@RequestParam("id") String id) {
//        GoodsInOutDO goodsInOut = goodsInOutService.getGoodsInOut(id);
//        return success(BeanUtils.toBean(goodsInOut, GoodsInOutRespVO.class));
//    }
//
//    @PostMapping("/page")
//    @ApiOperation(value = "获得附物管理-附物存入取出分页")
//    public CommonResult<PageResult<GoodsInOutRespVO>> getGoodsInOutPage(@Valid @RequestBody GoodsInOutPageReqVO pageReqVO) {
//        PageResult<GoodsInOutDO> pageResult = goodsInOutService.getGoodsInOutPage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, GoodsInOutRespVO.class));
//    }
//
//    @PostMapping("/list")
//    @ApiOperation(value = "获得附物管理-附物存入取出列表")
//    public CommonResult<List<GoodsInOutRespVO>> getGoodsInOutList(@Valid @RequestBody GoodsInOutListReqVO listReqVO) {
//        List<GoodsInOutDO> list = goodsInOutService.getGoodsInOutList(listReqVO);
//        return success(BeanUtils.toBean(list, GoodsInOutRespVO.class));
//    }
}
