package com.rs.module.ptm.domain.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 附物管理-储物柜子格子配置列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class GridListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("所属柜子ID")
    private String cabinetId;

    @ApiModelProperty("柜子编号，如A-01、B-12（区域-序号）")
    private String cabinetCode;

    @ApiModelProperty("格子编号，如A-01-01（柜子编号-格子序号）")
    private String gridNumber;

    @ApiModelProperty("格子尺寸，如S（小）/M（中）/L（大）")
    private String gridSize;

    @ApiModelProperty("格子状态：1-空闲/2-占用/3-故障（枚举值）")
    private Short status;

}
