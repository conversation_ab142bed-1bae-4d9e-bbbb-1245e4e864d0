package com.rs.module.ptm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.exception.ServiceException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ptm.dao.*;
import com.rs.module.ptm.domain.entity.*;
import com.rs.module.ptm.domain.vo.*;
import com.rs.module.ptm.enums.CabinetGridStatusEnum;
import com.rs.module.ptm.enums.GoodsDetailStatusEnum;
import com.rs.module.ptm.service.GoodsDetailService;
import com.rs.module.ptm.service.GoodsInOutService;
import com.rs.module.ptm.vo.GoodsDetailSaveReqVO;
import com.rs.util.DicUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 附物管理-附物明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class GoodsDetailServiceImpl extends BaseServiceImpl<GoodsDetailDao, GoodsDetailDO> implements GoodsDetailService {

    @Resource
    private GoodsDetailDao goodsDetailDao;

    @Resource
    private GoodsDao goodsDao;

    @Resource
    private GoodsInOutService goodsInOutService;

    @Resource
    private GoodsOutDetailDao goodsOutDetailDao;

    @Resource
    private GridDao gridDao;

    @Resource
    private CabinetDao cabinetDao;


    @Resource
    private BspApi bspApi;


    public List<GoodsDetailRespVO> getGoodsDetailList(String goodsId) {
        return this.lambdaQuery().eq(GoodsDetailDO::getPersonalGoodsId, goodsId).orderByDesc(GoodsDetailDO::getUpdateTime).list().stream().map(o -> {
            GoodsDetailRespVO goodsDetailRespVO = BeanUtils.toBean(o, GoodsDetailRespVO.class);
            if (StringUtils.hasText(o.getPhotoPath())) {
                goodsDetailRespVO.setPhotoPathList(Arrays.asList(o.getPhotoPath().split(",")));
            }
            return goodsDetailRespVO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<GoodsDetailRespVO> getDqwGoodsDetailList(String goodsId) {
        //获取所有存放状态的物品
        List<GoodsDetailDO> goodsDetailDOList = this.lambdaQuery().eq(GoodsDetailDO::getPersonalGoodsId, goodsId)
                .eq(GoodsDetailDO::getStatus, GoodsDetailStatusEnum.CR.getCode()).orderByDesc(GoodsDetailDO::getUpdateTime).list();
        if (CollectionUtils.isEmpty(goodsDetailDOList)) {
            return Collections.emptyList();
        }

        List<String> wpbhList = goodsDetailDOList.stream().map(GoodsDetailDO::getWpbh).collect(Collectors.toList());
        List<GoodsOutDetailDO> goodsOutDetailList = goodsOutDetailDao.selectList(new LambdaQueryWrapperX<GoodsOutDetailDO>().in(GoodsOutDetailDO::getWpbh, wpbhList));


        //获取所有取出记录
//        List<GoodsInOutDO> goodsOutList = goodsInOutService.lambdaQuery().in(GoodsInOutDO::getWpbh, wpbhList)
//                .eq(GoodsInOutDO::getGoodsType, "02")
//                .list();

        return goodsDetailDOList.stream().map(goodsDetailDO -> {
            Integer outQuantity = goodsOutDetailList.stream().filter(goodsOutDetail -> goodsOutDetail.getWpbh().equals(goodsDetailDO.getWpbh())).mapToInt(GoodsOutDetailDO::getTakeoutQuantity).sum();
            goodsDetailDO.setQuantity(goodsDetailDO.getQuantity() - outQuantity);
            GoodsDetailRespVO goodsDetailRespVO = BeanUtils.toBean(goodsDetailDO, GoodsDetailRespVO.class);
            if (StringUtils.hasText(goodsDetailDO.getPhotoPath())) {
                goodsDetailRespVO.setPhotoPathList(Arrays.asList(goodsDetailDO.getPhotoPath().split(",")));
            }
            return goodsDetailRespVO;
        }).filter(goodsDetailDO -> goodsDetailDO.getQuantity() > 0).collect(Collectors.toList());

    }

    @Override
    public List<GoodsItemsStoredListRespVO> getItemsTdoStoredListByJgrybm(String jgrybm, String ip) {
        List<GoodsItemsStoredListRespVO> respList = new ArrayList<>();

        //获取是那个柜子
        List<CabinetDO> cabinetList = cabinetDao.selectList(new LambdaQueryWrapperX<CabinetDO>().eq(CabinetDO::getIp, ip));
        if(CollUtil.isEmpty(cabinetList)){
            throw new ServiceException("传入IP有误！系统不存在");
        }
        List<GridDO> gridList = gridDao.selectList(new LambdaQueryWrapper<GridDO>().in(GridDO::getCabinetId, cabinetList.stream().map(CabinetDO::getId).collect(Collectors.toList())));

        Map<String, GridDO> mapGrid =  gridList.stream().collect(Collectors.toMap(GridDO::getId, Function.identity()));


        List<GoodsDetailDO> goodsDetailRespVOList = goodsDetailDao.selectList(new LambdaQueryWrapperX<GoodsDetailDO>()
                .eq(GoodsDetailDO::getJgrybm, jgrybm).eq(GoodsDetailDO::getStatus, GoodsDetailStatusEnum.YDJ.getCode())
                .orderByDesc(GoodsDetailDO::getUpdateTime));
        if (CollUtil.isNotEmpty(goodsDetailRespVOList)) {
            List<GoodsDetailRespVO> resultList = new ArrayList<>();
            for (GoodsDetailDO goodsDetailDO : goodsDetailRespVOList) {
                GoodsDetailRespVO goodsDetailRespVO = new GoodsDetailRespVO();
                BeanUtils.copyProperties( goodsDetailDO, goodsDetailRespVO);
                if(StrUtil.isNotBlank(goodsDetailDO.getPhotoPath())){
                    goodsDetailRespVO.setPhotoPathList(Arrays.asList(goodsDetailDO.getPhotoPath().split(",")));
                }
                resultList.add( goodsDetailRespVO);
            }

            //这块登记的时候，没有选择物品柜的
            for (GoodsDetailRespVO goodsDetailRespVO : resultList) {
                if(StrUtil.isBlank( goodsDetailRespVO.getStorageLocation()) ){
                    GoodsItemsStoredListRespVO goodsItems = new GoodsItemsStoredListRespVO();
                    goodsItems.setJgrybm(goodsDetailRespVO.getJgrybm());
                    goodsItems.setJgryxm(goodsDetailRespVO.getJgryxm());
                    GoodsItemsStoredListRespVO.NotStorageLocation notStorageLocation = new GoodsItemsStoredListRespVO.NotStorageLocation();
                    BeanUtils.copyProperties(goodsDetailRespVO, notStorageLocation);
                    goodsItems.setNotStorageLocation(notStorageLocation);
                    respList.add(goodsItems);
                }
            }


            Map<String, List<GoodsDetailRespVO>> map = resultList.stream()
                    .filter(e -> StrUtil.isNotBlank(e.getStorageLocation())).collect(Collectors.groupingBy(GoodsDetailRespVO::getStorageLocation));
            if (CollUtil.isNotEmpty(map)) {
                map.forEach((k, v) -> {
                    //ip 同一个主柜才写入  //后面跟前端-联调的时候再处理
                    if(mapGrid.containsKey(k)){
                        GoodsItemsStoredListRespVO goodsItems = new GoodsItemsStoredListRespVO();
                        goodsItems.setStorageLocation((mapGrid.get(k).getGridName()));
                        goodsItems.setResultList(v);
                        GoodsDetailRespVO goodsDetailRespVO = v.get(0);
                        goodsItems.setStorageLocationName(goodsDetailRespVO.getStorageLocationName());
                        goodsItems.setJgrybm(goodsDetailRespVO.getJgrybm());
                        goodsItems.setJgryxm(goodsDetailRespVO.getJgryxm());
                        respList.add(goodsItems);
                    }
                });
            }


        }
        return respList;
    }

    @Override
    public List<GoodsDetailRespVO> wpInfoByGoodsId(String goodsId) {
//获取所有存放状态的物品
        return this.lambdaQuery().eq(GoodsDetailDO::getPersonalGoodsId, goodsId)
                .orderByDesc(GoodsDetailDO::getUpdateTime).list().stream()
                .map(goodsDetailDO -> BeanUtils.toBean(goodsDetailDO, GoodsDetailRespVO.class)).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean depositItemsUpdateStatus(GoodsItemsStoredDepositReqVO reqVO) {
        List<GoodsDetailDO> goodsDetailRespVOList = goodsDetailDao.selectList(new LambdaQueryWrapperX<GoodsDetailDO>()
                .eq(GoodsDetailDO::getJgrybm, reqVO.getJgrybm()).eq(GoodsDetailDO::getStatus, GoodsDetailStatusEnum.YDJ.getCode()));
        if (CollUtil.isEmpty(goodsDetailRespVOList)) {
            return false;
        }

        //List<String> storageLocationList = reqVO.getStorageLocation();
        //更改为已入库。存在两个柜子，一个未入库，一个已入库情况。。。
        List<String> wpbhList = reqVO.getWpbhArrayList();
        List<GoodsDetailDO> arrayList = new ArrayList<>();
        for (GoodsDetailDO goodsDetailDO : goodsDetailRespVOList) {
            if (wpbhList.contains(goodsDetailDO.getWpbh() ) ) {
                goodsDetailDO.setStatus(GoodsDetailStatusEnum.CR.getCode());
                //goodsDetailDO.setId(null);
                arrayList.add(goodsDetailDO);
            }
        }

        if (CollUtil.isEmpty(arrayList)) {
            return false;
        }
        this.saveOrUpdateBatch(arrayList);
        //
        //int count = goodsDetailDao.selectCount(new LambdaQueryWrapperX<GoodsDetailDO>()
        //        .eq(GoodsDetailDO::getJgrybm, reqVO.getJgrybm()).eq(GoodsDetailDO::getStatus, GoodsDetailStatusEnum.YDJ.getCode()));
        //已存入了，直接修改状态为已入库
        //if(count == 0){
        List<String> list = arrayList.stream().map(GoodsDetailDO::getPersonalGoodsId).distinct().collect(Collectors.toList());
        GoodsDO goodsDO = new GoodsDO();
        goodsDO.setStatus(GoodsDetailStatusEnum.CR.getCode());
        goodsDao.update(goodsDO, new LambdaUpdateWrapper<GoodsDO>().in(GoodsDO::getId, list));
        //}
        return true;
    }

    @Override
    public List<GoodsItemsStoredListRespVO> getTaskOutListByJgrybm(String jgrybm, String ip) {
        List<GoodsItemsStoredListRespVO> respList = new ArrayList<>();

        ////获取是那个柜子
        List<CabinetDO> cabinetList = cabinetDao.selectList(new LambdaQueryWrapperX<CabinetDO>().eq(CabinetDO::getIp, ip));
        if(CollUtil.isEmpty(cabinetList)){
            throw new ServiceException("传入IP有误！系统不存在");
        }
        List<GridDO> gridList = gridDao.selectList(new LambdaQueryWrapper<GridDO>().in(GridDO::getCabinetId, cabinetList.stream().map(CabinetDO::getId).collect(Collectors.toList())));

        Map<String, GridDO> mapGrid =  gridList.stream().collect(Collectors.toMap(GridDO::getId, Function.identity()));


        List<GoodsDetailDO> goodsDetailRespVOList = goodsDetailDao.selectList(new LambdaQueryWrapperX<GoodsDetailDO>()
                .eq(GoodsDetailDO::getJgrybm, jgrybm).eq(GoodsDetailDO::getStatus, GoodsDetailStatusEnum.YDJDQC.getCode())
                .orderByDesc(GoodsDetailDO::getUpdateTime));
        if (CollUtil.isNotEmpty(goodsDetailRespVOList)) {
            List<GoodsDetailRespVO> resultList = new ArrayList<>();
            for (GoodsDetailDO goodsDetailDO : goodsDetailRespVOList) {
                GoodsDetailRespVO goodsDetailRespVO = new GoodsDetailRespVO();
                BeanUtils.copyProperties( goodsDetailDO, goodsDetailRespVO);
                if(StrUtil.isNotBlank(goodsDetailDO.getPhotoPath())){
                    goodsDetailRespVO.setPhotoPathList(Arrays.asList(goodsDetailDO.getPhotoPath().split(",")));
                }
                resultList.add( goodsDetailRespVO);
            }

            Map<String, List<GoodsDetailRespVO>> map = resultList.stream()
                    .filter(e -> StrUtil.isNotBlank(e.getStorageLocation())).collect(Collectors.groupingBy(GoodsDetailRespVO::getStorageLocation));
            if (CollUtil.isNotEmpty(map)) {
                map.forEach((k, v) -> {
                    //ip 同一个主柜才写入  //后面跟前端-联调的时候再处理
                    if(mapGrid.containsKey(k)){
                        GoodsItemsStoredListRespVO goodsItems = new GoodsItemsStoredListRespVO();
                        goodsItems.setStorageLocation((mapGrid.get(k).getGridName()));
                        goodsItems.setResultList(v);
                        GoodsDetailRespVO goodsDetailRespVO = v.get(0);
                        goodsItems.setStorageLocationName(goodsDetailRespVO.getStorageLocationName());
                        goodsItems.setJgrybm(goodsDetailRespVO.getJgrybm());
                        goodsItems.setJgryxm(goodsDetailRespVO.getJgryxm());
                        respList.add(goodsItems);
                    }
                });
            }


        }
        return respList;
    }

    @Override
    public Boolean taskOutItemsUpdateStatus(GoodsItemsStoredDepositReqVO reqVO) {
        List<GoodsDetailDO> goodsDetailRespVOList = goodsDetailDao.selectList(new LambdaQueryWrapperX<GoodsDetailDO>()
                .eq(GoodsDetailDO::getJgrybm, reqVO.getJgrybm()).eq(GoodsDetailDO::getStatus, GoodsDetailStatusEnum.YDJDQC.getCode()));
        if (CollUtil.isEmpty(goodsDetailRespVOList)) {
            return false;
        }


        //List<String> storageLocationList = reqVO.getStorageLocation();
        //更改为已入库。存在两个柜子，一个未入库，一个已入库情况。。。

        List<String> wpbhList =  reqVO.getWpbhArrayList();
        List<GoodsDetailDO> arrayList = new ArrayList<>();
        for (GoodsDetailDO goodsDetailDO : goodsDetailRespVOList) {
            if (wpbhList.contains(goodsDetailDO.getWpbh() ) ) {
                goodsDetailDO.setStatus(GoodsDetailStatusEnum.QC.getCode());
                //goodsDetailDO.setId(null);
                arrayList.add(goodsDetailDO);
            }
        }

        if (CollUtil.isEmpty(arrayList)) {
            return false;
        }
        this.saveOrUpdateBatch(arrayList);

        List<GoodsDetailDO> goodsDetailDOList = goodsDetailDao.selectList(new LambdaQueryWrapperX<GoodsDetailDO>()
                .eq(GoodsDetailDO::getJgrybm, reqVO.getJgrybm()));

        boolean allStatusOne = goodsDetailDOList.stream()
                .allMatch(goods -> GoodsDetailStatusEnum.QC.getCode().equals(goods.getStatus()));
        if(allStatusOne){
            List<String> list = arrayList.stream().map(GoodsDetailDO::getPersonalGoodsId).distinct().collect(Collectors.toList());
            GoodsDO goodsDO = new GoodsDO();
            goodsDO.setStatus(GoodsDetailStatusEnum.QC.getCode());
            goodsDao.update(goodsDO, new LambdaUpdateWrapper<GoodsDO>().in(GoodsDO::getId, list));
        }
        return true;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createGoodsDetail(GoodsDetailSaveReqVO createReqVO) {
        // 插入
        GoodsDetailDO goodsDetail = BeanUtils.toBean(createReqVO, GoodsDetailDO.class);
        goodsDetail.setStatus(GoodsDetailStatusEnum.YDJ.getCode());

        List<String> photoPathList = createReqVO.getPhotoPathList();
        goodsDetail.setPhotoCount(0);
        if (!CollectionUtils.isEmpty(photoPathList)) {
            goodsDetail.setPhotoPath(String.join(",", photoPathList));
            goodsDetail.setPhotoCount(photoPathList.size());
        }
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        String idCard = sessionUser.getIdCard();
        String name = sessionUser.getName();
        String orgCode = sessionUser.getOrgCode();
        goodsDetail.setOperatorSfzh(idCard);
        goodsDetail.setOperatorXm(name);
        goodsDetail.setOperatorTime(new Date());
        goodsDetail.setWpbh(StringUtil.getGuid32());
        goodsDetail.setNameName(DicUtils.translate("ZD_FWGLWPZL", goodsDetail.getNameName()));
        JSONObject formData = JSONUtil.createObj().set("orgCode", orgCode);
        goodsDetail.setWpbh(bspApi.executeByRuleCode("ptm_goods_no", formData.toJSONString(4)));
        if (StringUtils.hasText(goodsDetail.getId())) {
            goodsDetailDao.updateById(goodsDetail);
        } else {
            goodsDetailDao.insert(goodsDetail);
        }

        GridDO grid = gridDao.selectById(createReqVO.getStorageLocation());
        if(!CabinetGridStatusEnum.KX.getCode().equals(grid.getStatus())){
            throw new ServiceException("该柜子已被占用或者故障，请重新选择！");
        }
        if(grid != null){
            grid.setStatus(CabinetGridStatusEnum.ZY.getCode());
            gridDao.updateById(grid);
        }
        // 返回
        return goodsDetail.getId();
    }

    @Override
    public void updateGoodsDetail(GoodsDetailSaveReqVO updateReqVO) {
        // 校验存在
        validateGoodsDetailExists(updateReqVO.getId());
        // 更新
        GoodsDetailDO updateObj = BeanUtils.toBean(updateReqVO, GoodsDetailDO.class);
        goodsDetailDao.updateById(updateObj);
    }

    @Override
    public void deleteGoodsDetail(String id) {
        // 校验存在
        validateGoodsDetailExists(id);
        // 删除
        goodsDetailDao.deleteById(id);
    }

    private void validateGoodsDetailExists(String id) {
        if (goodsDetailDao.selectById(id) == null) {
            throw new ServerException("附物管理-附物明细数据不存在");
        }
    }

    @Override
    public GoodsDetailDO getGoodsDetail(String id) {
        return goodsDetailDao.selectById(id);
    }

    @Override
    public PageResult<GoodsDetailDO> getGoodsDetailPage(GoodsDetailPageReqVO pageReqVO) {
        return goodsDetailDao.selectPage(pageReqVO);
    }

    @Override
    public List<GoodsDetailDO> getGoodsDetailList(GoodsDetailListReqVO listReqVO) {
        return goodsDetailDao.selectList(listReqVO);
    }


}
