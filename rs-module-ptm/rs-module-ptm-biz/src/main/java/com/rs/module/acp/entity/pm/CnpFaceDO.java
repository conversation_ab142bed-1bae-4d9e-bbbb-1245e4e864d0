package com.rs.module.acp.entity.pm;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-监管管理-仓内外屏人脸信息维护 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pm_cnp_face")
@KeySequence("acp_pm_cnp_face_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pm_cnp_face")
public class CnpFaceDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 人员类型 1警员、2在押人员
     */
    private Short personnelType;
    /**
     * 人员编号、警员id
     */
    private String personnelId;
    /**
     * 照片
     */
    private String photo;

}
