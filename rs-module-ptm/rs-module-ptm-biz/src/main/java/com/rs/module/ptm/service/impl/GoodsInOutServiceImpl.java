package com.rs.module.ptm.service.impl;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ptm.dao.GoodsInOutDao;
import com.rs.module.ptm.domain.entity.GoodsDetailDO;
import com.rs.module.ptm.domain.entity.GoodsInOutDO;
import com.rs.module.ptm.domain.vo.GoodsInOutListReqVO;
import com.rs.module.ptm.domain.vo.GoodsInOutPageReqVO;
import com.rs.module.ptm.domain.vo.GoodsInOutSaveReqVO;
import com.rs.module.ptm.enums.GoodsDetailStatusEnum;
import com.rs.module.ptm.service.GoodsInOutService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


/**
 * 附物管理-附物存入取出 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class GoodsInOutServiceImpl extends BaseServiceImpl<GoodsInOutDao, GoodsInOutDO> implements GoodsInOutService {

    @Resource
    private GoodsInOutDao goodsInOutDao;


    @Override
    public String saveGoodsInOut(GoodsDetailDO goodsDetailDO) {
        GoodsInOutDO goodsInOutDO = BeanUtils.toBean(goodsDetailDO, GoodsInOutDO.class);
        goodsInOutDO.setId(null);
        goodsInOutDO.setBusinessId(goodsDetailDO.getPersonalGoodsId());
        if (GoodsDetailStatusEnum.CR.getCode().equals(goodsDetailDO.getStatus())
                || GoodsDetailStatusEnum.YDJ.getCode().equals(goodsDetailDO.getStatus())
        ) {
            goodsInOutDO.setInOutType("01");
        } else if (GoodsDetailStatusEnum.QC.getCode().equals(goodsDetailDO.getStatus())
                || GoodsDetailStatusEnum.YDJDQC.getCode().equals(goodsDetailDO.getStatus())
        ) {
            goodsInOutDO.setInOutType("02");
        }

        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        String idCard = sessionUser.getIdCard();
        String name = sessionUser.getName();
        goodsInOutDO.setOperatorSfzh(idCard);
        goodsInOutDO.setOperatorXm(name);
        goodsInOutDO.setOperatorTime(new Date());
        this.save(goodsInOutDO);
        return goodsInOutDO.getId();
    }

    @Override
    public String createGoodsInOut(GoodsInOutSaveReqVO createReqVO) {
        // 插入
        GoodsInOutDO goodsInOut = BeanUtils.toBean(createReqVO, GoodsInOutDO.class);
        goodsInOutDao.insert(goodsInOut);
        // 返回
        return goodsInOut.getId();
    }

    @Override
    public void updateGoodsInOut(GoodsInOutSaveReqVO updateReqVO) {
        // 校验存在
        validateGoodsInOutExists(updateReqVO.getId());
        // 更新
        GoodsInOutDO updateObj = BeanUtils.toBean(updateReqVO, GoodsInOutDO.class);
        goodsInOutDao.updateById(updateObj);
    }

    @Override
    public void deleteGoodsInOut(String id) {
        // 校验存在
        validateGoodsInOutExists(id);
        // 删除
        goodsInOutDao.deleteById(id);
    }

    private void validateGoodsInOutExists(String id) {
        if (goodsInOutDao.selectById(id) == null) {
            throw new ServerException("附物管理-附物存入取出数据不存在");
        }
    }

    @Override
    public GoodsInOutDO getGoodsInOut(String id) {
        return goodsInOutDao.selectById(id);
    }

    @Override
    public PageResult<GoodsInOutDO> getGoodsInOutPage(GoodsInOutPageReqVO pageReqVO) {
        return goodsInOutDao.selectPage(pageReqVO);
    }

    @Override
    public List<GoodsInOutDO> getGoodsInOutList(GoodsInOutListReqVO listReqVO) {
        return goodsInOutDao.selectList(listReqVO);
    }


}
