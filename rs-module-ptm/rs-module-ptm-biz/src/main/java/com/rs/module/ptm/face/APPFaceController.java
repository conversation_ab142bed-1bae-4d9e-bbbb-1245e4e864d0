package com.rs.module.ptm.face;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.gosun.zhjg.common.msg.R;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.CnpFaceForm;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.mybatis.util.BspDbUtil;
import com.rs.module.ptm.controller.admin.face.WpgUserVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

@Api(tags = "实战平台-移动端-人脸管理")
@RestController
@RequestMapping("/app/acp/face")
@Validated
public class APPFaceController {


    @GetMapping("/getFaceList")
    public CommonResult<List<WpgUserVO>> getFaceList(@RequestParam(required = false) String orgCode) {
        return CommonResult.success(WpgUserVO.toBean(WpgUser.class));
    }

}
