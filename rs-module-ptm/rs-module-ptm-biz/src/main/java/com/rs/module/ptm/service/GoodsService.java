package com.rs.module.ptm.service;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.ptm.domain.entity.GoodsDO;
import com.rs.module.ptm.domain.vo.*;
import com.rs.module.ptm.vo.GoodsSaveReqVO;

import javax.validation.Valid;
import java.util.List;

/**
 * 附物管理-附物 Service 接口
 *
 * <AUTHOR>
 */
public interface GoodsService extends IBaseService<GoodsDO> {

    /**
     * 创建附物管理-附物
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createGoods(@Valid GoodsSaveReqVO createReqVO);

    /**
     * 更新附物管理-附物
     *
     * @param updateReqVO 更新信息
     */
    void updateGoods(@Valid GoodsSaveReqVO updateReqVO);

    /**
     * 删除附物管理-附物
     *
     * @param id 编号
     */
    void deleteGoods(String id);

    /**
     * 获得附物管理-附物
     *
     * @param id 编号
     * @return 附物管理-附物
     */
    GoodsRespVO getGoods(String id);

    /**
    * 获得附物管理-附物分页
    *
    * @param pageReqVO 分页查询
    * @return 附物管理-附物分页
    */
    PageResult<GoodsDO> getGoodsPage(GoodsPageReqVO pageReqVO);

    /**
    * 获得附物管理-附物列表
    *
    * @param listReqVO 查询条件
    * @return 附物管理-附物列表
    */
    List<GoodsDO> getGoodsList(GoodsListReqVO listReqVO);


    List<GoodsRespVO> getDcwByJgrybm(String jgrybm);

    String storageGoods(String id);

    String takeOutGoods(String id);

    GoodsRespVO getDetailsById(String id);

    GoodsRespVO getDqwGoodsDetailList(String id);

    List<GoodsLogRespVO> goodsLogList(String id);

    Object goodsLogListDetail(String id,String businessType);

    List<GoodsLogRespVO> goodsLogListTotal(String id);

    WpStatisticRespVO wpStatisticInfo(String wpbh);
}
