package com.rs.module.acp.controller.admin.pm.vo;

import lombok.Data;

import java.util.Date;


import io.swagger.annotations.ApiModelProperty;

@Data
public class WpgUserVO {

    @ApiModelProperty(value = "用户唯一标识ID")
    private String id;

    @ApiModelProperty(value = "登录账号", example = "zhangsan")
    private String loginId;

    @ApiModelProperty(value = "登录密码", example = "encryptedPassword123", hidden = true) // 建议隐藏密码
    private String password;

    @ApiModelProperty(value = "用户姓名", example = "张三")
    private String name;

    @ApiModelProperty(value = "用户年龄", example = "28")
    private String age;

    @ApiModelProperty(value = "用户性别：0-女，1-男", example = "1")
    private String sex;

    @ApiModelProperty(value = "用户头像URL", example = "https://example.com/avatar.png")
    private String avatar;

    @ApiModelProperty(value = "用户特征信息（如生物特征等）", example = "face_encoding_123")
    private String feature;

    @ApiModelProperty(value = "用户角色权限（默认为3）", example = "3")
    private String role;

    @ApiModelProperty(value = "最后登录时间", example = "2025-04-05T10:00:00")
    private Date loginTime;

    @ApiModelProperty(value = "创建时间", example = "2025-04-01T08:30:00")
    private Date createTime;

    @ApiModelProperty(value = "更新时间", example = "2025-04-05T10:00:00")
    private Date updateTime;

    @ApiModelProperty(value = "是否删除标记：N-未删除，Y-已删除", example = "N")
    private String isDeleted;

    @ApiModelProperty(value = "关联警员ID", example = "1001")
    private String associatedPoliceId;

    @ApiModelProperty(value = "关联警员姓名", example = "李警官")
    private String associatedPoliceName;

    @ApiModelProperty(value = "证件号码", example = "110101199001011234")
    private String certificateNumber;

    @ApiModelProperty(value = "警号", example = "J12345")
    private String policeNumber;

    @ApiModelProperty(value = "关联ISCDS信息", example = "ISCDS-001")
    private String associatedIscds;

    @ApiModelProperty(value = "用户类型", example = "police, civilian")
    private String userType;

    @ApiModelProperty(value = "人脸图像Base64或URL", example = "image/png;base64,...")
    private String faceImg;

    @ApiModelProperty(value = "手势信息编码", example = "gesture_wave_1")
    private String gesture;

    @ApiModelProperty(value = "指纹信息（加密存储）", example = "fingerprint_hash_abc")
    private String fingerprint;

    @ApiModelProperty(value = "关联警员编号（与警号可能不同）", example = "P98765")
    private String associatedPoliceNumber;
}
