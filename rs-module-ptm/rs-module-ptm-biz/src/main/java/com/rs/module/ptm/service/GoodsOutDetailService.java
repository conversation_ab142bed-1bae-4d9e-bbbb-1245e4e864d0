package com.rs.module.ptm.service;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.ptm.domain.entity.GoodsOutDetailDO;
import com.rs.module.ptm.domain.vo.GoodsOutDetailListReqVO;
import com.rs.module.ptm.domain.vo.GoodsOutDetailPageReqVO;
import com.rs.module.ptm.domain.vo.GoodsOutDetailSaveReqVO;

import javax.validation.Valid;
import java.util.List;

/**
 * 附物管理-附物取出登记明细 Service 接口
 *
 * <AUTHOR>
 */
public interface GoodsOutDetailService extends IBaseService<GoodsOutDetailDO>{

    /**
     * 创建附物管理-附物取出登记明细
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createGoodsOutDetail(@Valid GoodsOutDetailSaveReqVO createReqVO);

    /**
     * 更新附物管理-附物取出登记明细
     *
     * @param updateReqVO 更新信息
     */
    void updateGoodsOutDetail(@Valid GoodsOutDetailSaveReqVO updateReqVO);

    /**
     * 删除附物管理-附物取出登记明细
     *
     * @param id 编号
     */
    void deleteGoodsOutDetail(String id);

    /**
     * 获得附物管理-附物取出登记明细
     *
     * @param id 编号
     * @return 附物管理-附物取出登记明细
     */
    GoodsOutDetailDO getGoodsOutDetail(String id);

    /**
    * 获得附物管理-附物取出登记明细分页
    *
    * @param pageReqVO 分页查询
    * @return 附物管理-附物取出登记明细分页
    */
    PageResult<GoodsOutDetailDO> getGoodsOutDetailPage(GoodsOutDetailPageReqVO pageReqVO);

    /**
    * 获得附物管理-附物取出登记明细列表
    *
    * @param listReqVO 查询条件
    * @return 附物管理-附物取出登记明细列表
    */
    List<GoodsOutDetailDO> getGoodsOutDetailList(GoodsOutDetailListReqVO listReqVO);


}
