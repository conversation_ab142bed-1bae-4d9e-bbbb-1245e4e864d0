package com.rs.module.ptm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.exception.ServiceException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ptm.dao.GoodsDetailDao;
import com.rs.module.ptm.dao.GoodsOutDetailDao;
import com.rs.module.ptm.domain.entity.GoodsDetailDO;
import com.rs.module.ptm.domain.entity.GoodsOutDetailDO;
import com.rs.module.ptm.domain.vo.GoodsOutDetailListReqVO;
import com.rs.module.ptm.domain.vo.GoodsOutDetailPageReqVO;
import com.rs.module.ptm.domain.vo.GoodsOutDetailSaveReqVO;
import com.rs.module.ptm.enums.GoodsDetailStatusEnum;
import com.rs.module.ptm.service.GoodsOutDetailService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;


/**
 * 附物管理-附物取出登记明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class GoodsOutDetailServiceImpl extends BaseServiceImpl<GoodsOutDetailDao, GoodsOutDetailDO> implements GoodsOutDetailService {

    @Resource
    private GoodsOutDetailDao goodsOutDetailDao;

    @Resource
    private GoodsDetailDao goodsDetailDao;

    @Override
    public String createGoodsOutDetail(GoodsOutDetailSaveReqVO createReqVO) {
        GoodsOutDetailDO goodsOutDetail = new GoodsOutDetailDO();
        GoodsDetailDO goodsDetail = goodsDetailDao.getByWpbh(createReqVO.getWpbh());
        //获取已经取出登记过的判断是否超出
        int existsTakeOut = this.lambdaQuery().eq(GoodsOutDetailDO::getWpbh, goodsDetail.getWpbh()).list()
                .stream().mapToInt(GoodsOutDetailDO::getTakeoutQuantity).sum();
        //超出可以登记的数量了
        if ((createReqVO.getTakeoutQuantity() + existsTakeOut) > goodsDetail.getQuantity()) {
            throw new ServiceException("物品登记数量超出存放数量，请重试");
        }
        BeanUtil.copyProperties(goodsDetail,goodsOutDetail);
        BeanUtil.copyProperties(createReqVO,goodsOutDetail);

        goodsDetailDao.updateById(goodsDetail);

        if ((createReqVO.getTakeoutQuantity() + existsTakeOut) == goodsDetail.getQuantity()) {
            //物品全部取出才改状态
            goodsDetail.setStatus(GoodsDetailStatusEnum.YDJDQC.getCode());
        }
//        goodsOutDetail.setStatus(GoodsDetailStatusEnum.YDJ.getCode());
        // 更新
        if (StringUtils.hasText(goodsOutDetail.getId())) {
            goodsOutDetailDao.updateById(goodsOutDetail);
        } else {
            goodsOutDetailDao.insert(goodsOutDetail);
        }
        // 返回
        return goodsOutDetail.getId();
    }

    @Override
    public void updateGoodsOutDetail(GoodsOutDetailSaveReqVO updateReqVO) {
        // 校验存在
        validateGoodsOutDetailExists(updateReqVO.getId());
        // 更新
        GoodsOutDetailDO updateObj = BeanUtils.toBean(updateReqVO, GoodsOutDetailDO.class);
        goodsOutDetailDao.updateById(updateObj);
    }

    @Override
    public void deleteGoodsOutDetail(String id) {
        // 校验存在
        validateGoodsOutDetailExists(id);
        // 删除
        goodsOutDetailDao.deleteById(id);
    }

    private void validateGoodsOutDetailExists(String id) {
        if (goodsOutDetailDao.selectById(id) == null) {
            throw new ServerException("附物管理-附物取出登记明细数据不存在");
        }
    }

    @Override
    public GoodsOutDetailDO getGoodsOutDetail(String id) {
        return goodsOutDetailDao.selectById(id);
    }

    @Override
    public PageResult<GoodsOutDetailDO> getGoodsOutDetailPage(GoodsOutDetailPageReqVO pageReqVO) {
        return goodsOutDetailDao.selectPage(pageReqVO);
    }

    @Override
    public List<GoodsOutDetailDO> getGoodsOutDetailList(GoodsOutDetailListReqVO listReqVO) {
        return goodsOutDetailDao.selectList(listReqVO);
    }


}
