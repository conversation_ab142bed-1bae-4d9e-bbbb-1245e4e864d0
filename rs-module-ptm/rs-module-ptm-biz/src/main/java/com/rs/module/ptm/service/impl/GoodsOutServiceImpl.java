package com.rs.module.ptm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.common.util.validation.ValidationUtils;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.base.util.BspApprovalUtil;
import com.rs.module.ptm.dao.GoodsDetailDao;
import com.rs.module.ptm.dao.GoodsOutDao;
import com.rs.module.ptm.domain.entity.GoodsDetailDO;
import com.rs.module.ptm.domain.entity.GoodsInOutDO;
import com.rs.module.ptm.domain.entity.GoodsOutDO;
import com.rs.module.ptm.domain.entity.GoodsOutDetailDO;
import com.rs.module.ptm.domain.vo.*;
import com.rs.module.ptm.enums.GoodsDetailStatusEnum;
import com.rs.module.ptm.enums.GoodsStatusEnum;
import com.rs.module.ptm.enums.GoodsTakeoutReasonEnum;
import com.rs.module.ptm.service.GoodsDetailService;
import com.rs.module.ptm.service.GoodsInOutService;
import com.rs.module.ptm.service.GoodsOutDetailService;
import com.rs.module.ptm.service.GoodsOutService;
import com.rs.util.DicUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 附物管理-附物取出登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class GoodsOutServiceImpl extends BaseServiceImpl<GoodsOutDao, GoodsOutDO> implements GoodsOutService {

    @Resource
    private GoodsOutDao goodsOutDao;

    @Resource
    private GoodsOutDetailService goodsOutDetailService;

    @Resource
    private GoodsDetailService goodsDetailService;

    @Resource
    private GoodsInOutService goodsInOutService;

    @Resource
    private GoodsDetailDao goodsDetailDao;



    private static final String PROCESS_KEY = "fwgllsqwlc";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createGoodsOut(GoodsOutSaveReqVO createReqVO) {
        GoodsOutDO goodsOut = BeanUtils.toBean(createReqVO, GoodsOutDO.class);
        List<GoodsOutDetailSaveReqVO> goodsOutDetailSaveReqVOList = createReqVO.getGoodsOutDetailSaveReqVOList();
        //校验
        goodsOutDetailSaveReqVOList.forEach(ValidationUtils::validate);
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        String idCard = sessionUser.getIdCard();
        String name = sessionUser.getName();
        if (!CollectionUtils.isEmpty(createReqVO.getProofMaterialsPathList())) {
            goodsOut.setProofMaterialsPath(String.join(",", createReqVO.getProofMaterialsPathList()));
        }
        if (StringUtils.hasText(goodsOut.getId())) {
            goodsOutDao.updateById(goodsOut);
        } else {
            goodsOut.setStatus(GoodsStatusEnum.YDJ.getCode());
            goodsOut.setOperatorSfzh(idCard);
            goodsOut.setOperatorXm(name);
            goodsOut.setOperatorTime(new Date());

            //判断是否是临时取物
            if (GoodsTakeoutReasonEnum.LSQW.getCode().equals(goodsOut.getTakeoutReason()) || GoodsTakeoutReasonEnum.LSQWKQ.getCode().equals(goodsOut.getTakeoutReason())) {
                //临时取物的话需要审批
                Map<String, Object> variables = new HashMap<>();
                String msgTit = "";
                String msgUrl = "";
                goodsOut.setId(StringUtil.getGuid32());
                //启动流程
                Map<String, String> processResult = BspApprovalUtil.commonStartProcessMap(PROCESS_KEY, goodsOut.getId(), msgTit, msgUrl, variables, HttpUtils.getAppCode());
                goodsOut.setActInstId(processResult.get("actInstId"));
                goodsOut.setTaskId(processResult.get("taskId"));
            }
            goodsOutDao.insert(goodsOut);
        }

        GoodsOutDO finalGoodsOut = goodsOut;
        goodsOutDetailSaveReqVOList.forEach(goodsOutDetailSaveReqVO -> {
            goodsOutDetailSaveReqVO.setJgrybm(finalGoodsOut.getJgrybm());
            goodsOutDetailSaveReqVO.setJgryxm(finalGoodsOut.getJgryxm());
            goodsOutDetailSaveReqVO.setPersonalGoodsOutId(finalGoodsOut.getId());
            goodsOutDetailService.createGoodsOutDetail(goodsOutDetailSaveReqVO);

            //存入取出记录
            GoodsInOutDO goodsInOutDO = new GoodsInOutDO();
            GoodsDetailDO goodsDetail = goodsDetailDao.getByWpbh(goodsOutDetailSaveReqVO.getWpbh());
            BeanUtils.copyProperties(goodsDetail,goodsInOutDO);
            BeanUtils.copyProperties(goodsOutDetailSaveReqVO,goodsInOutDO);
            BeanUtils.copyProperties(createReqVO,goodsInOutDO);
            goodsInOutDO.setId(null);
            goodsInOutDO.setQuantity(goodsOutDetailSaveReqVO.getTakeoutQuantity());
            goodsInOutDO.setStatus(GoodsDetailStatusEnum.YDJDQC.getCode());
            goodsInOutDO.setInOutType("02");
            goodsInOutDO.setBusinessId(finalGoodsOut.getId());
            goodsInOutDO.setOperatorSfzh(idCard);
            goodsInOutDO.setOperatorXm(name);
            goodsInOutDO.setOperatorTime(new Date());
            goodsInOutService.save(goodsInOutDO);
        });



        // 返回
        return goodsOut.getId();
    }

    @Override
    public void updateGoodsOut(GoodsOutSaveReqVO updateReqVO) {
        // 校验存在
        validateGoodsOutExists(updateReqVO.getId());
        // 更新
        GoodsOutDO updateObj = BeanUtils.toBean(updateReqVO, GoodsOutDO.class);
        goodsOutDao.updateById(updateObj);
    }

    @Override
    public void deleteGoodsOut(String id) {
        // 校验存在
        validateGoodsOutExists(id);
        // 删除
        goodsOutDao.deleteById(id);
    }

    private void validateGoodsOutExists(String id) {
        if (goodsOutDao.selectById(id) == null) {
            throw new ServerException("附物管理-附物取出登记数据不存在");
        }
    }

    @Override
    public GoodsOutDO getGoodsOut(String id) {
        return goodsOutDao.selectById(id);
    }

    @Override
    public PageResult<GoodsOutDO> getGoodsOutPage(GoodsOutPageReqVO pageReqVO) {
        return goodsOutDao.selectPage(pageReqVO);
    }

    @Override
    public List<GoodsOutDO> getGoodsOutList(GoodsOutListReqVO listReqVO) {
        return goodsOutDao.selectList(listReqVO);
    }

    @Override
    public List<GoodsOutRespVO> getDqwByJgrybm(String jgrybm) {
        return this.lambdaQuery().eq(GoodsOutDO::getJgrybm, jgrybm)
                .eq(GoodsOutDO::getStatus, GoodsStatusEnum.YDJ.getCode())
                .orderByDesc(GoodsOutDO::getAddTime)
                .list().stream().filter(goodsOut -> {
                    if (GoodsTakeoutReasonEnum.LSQW.getCode().equals(goodsOut.getTakeoutReason()) || GoodsTakeoutReasonEnum.LSQWKQ.getCode().equals(goodsOut.getTakeoutReason())) {
                        //临时取出 需要审批通过才能进行下一步
                        return "1".equals(goodsOut.getApprovalResult());
                    }
                    return true;
                }).map(this::toGoodsOutRespVO).collect(Collectors.toList());
    }

    private GoodsOutRespVO toGoodsOutRespVO(GoodsOutDO goodsOut) {
        GoodsOutRespVO goodsOutRespVO = BeanUtils.toBean(goodsOut, GoodsOutRespVO.class);
        if (StringUtils.hasText(goodsOut.getProofMaterialsPath())) {
            goodsOutRespVO.setProofMaterialsPathList(Arrays.asList(goodsOut.getProofMaterialsPath().split(",")));
        }
        String id = goodsOut.getId();
        List<GoodsOutDetailDO> list = goodsOutDetailService.lambdaQuery().eq(GoodsOutDetailDO::getPersonalGoodsOutId, id).orderByDesc(GoodsOutDetailDO::getAddTime).list();
        List<String> wpbhList = list.stream().map(GoodsOutDetailDO::getWpbh).collect(Collectors.toList());
        List<GoodsDetailDO> goodsDetailDOList = goodsDetailService.lambdaQuery().in(GoodsDetailDO::getWpbh, wpbhList).list();
        List<GoodsOutDetailRespVO> goodsOutDetailRespVOList = list.stream()
                .map(goodsOutDetailDO -> {
                    GoodsOutDetailRespVO respVO = new GoodsOutDetailRespVO();
                    String wpbh = goodsOutDetailDO.getWpbh();
                    GoodsDetailDO goodsDetail = goodsDetailDOList
                            .stream().filter(goodsDetailDO -> goodsDetailDO.getWpbh().equals(wpbh))
                            .findFirst().orElse(null);
                    BeanUtils.copyProperties(goodsDetail, respVO);
                    if (StringUtils.hasText(goodsDetail.getPhotoPath())) {
                        respVO.setPhotoPathList(Arrays.asList(goodsDetail.getPhotoPath().split(",")));
                    }
                    BeanUtils.copyProperties(goodsOutDetailDO, respVO);
                    return respVO;
                })
                .collect(Collectors.toList());
        goodsOutRespVO.setGoodsOutDetailRespVOList(goodsOutDetailRespVOList);
        return goodsOutRespVO;
    }

    @Override
    public GoodsOutRespVO getDetailsById(String id) {
        GoodsOutDO goodsOut = this.getById(id);
        if (goodsOut == null) {
            throw new ServerException("附物管理-附物取出登记数据不存在");
        }
        return toGoodsOutRespVO(goodsOut);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean takeOutGoodsApproval(GoodsOutApprovalVO approvalVO) {
        //从当前登录用户信息设置审批信息默认值
        if (StringUtil.isEmpty(approvalVO.getApproverXm()))
            approvalVO.setApproverXm(SessionUserUtil.getSessionUser().getName());
        if (StringUtil.isEmpty(approvalVO.getApproverSfzh()))
            approvalVO.setApproverSfzh(SessionUserUtil.getSessionUser().getIdCard());
        if (approvalVO.getApproverTime() == null) approvalVO.setApproverTime(new Date());

        GoodsOutDO goodsOut = this.getById(approvalVO.getId());
        BspApproceStatusEnum isApprove = BspApproceStatusEnum.NOT_PASSED;
        //调用流程审批接口
        String msgTit = "";
        String msgUrl = "";
        approvalVO.setActInstId(goodsOut.getActInstId());
        approvalVO.setTaskId(goodsOut.getTaskId());
        approvalVO.setDefKey(PROCESS_KEY);
        BeanUtil.copyProperties(approvalVO, goodsOut);
        if (goodsOut.getApprovalResult().equals("1")) {//審批同意 ZD_DDGY_SPZT
            isApprove = BspApproceStatusEnum.PASSED;
        }
        Map<String, String> approvalResult = BspApprovalUtil.approvalProcessMap(approvalVO,
                isApprove,
                msgTit,
                msgUrl,
                true,
                null,
                null, HttpUtils.getAppCode()
        );
        String taskId = approvalResult.get("taskId"); // 更新任务ID
        goodsOut.setTaskId(taskId);
        this.updateById(goodsOut);
        return true;
    }

    @Override
    public String takeOutGoods(String id) {
        return null;
    }

    @Override
    public PageResult<GoodsOutApprovalRespVO> takeOutGoodsApprovalPage(GoodsOutApprovalPageReqVO pageReqVO) {
        Page<GoodsOutDO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        LambdaQueryWrapper<GoodsOutDO> wrapper = new LambdaQueryWrapperX<GoodsOutDO>()
                .eq(GoodsOutDO::getPersonalGoodsId, pageReqVO.getId()).orderByDesc(GoodsOutDO::getAddTime)
                .and(a -> a.eq(GoodsOutDO::getTakeoutReason, GoodsTakeoutReasonEnum.LSQW.getCode())
                        .or().eq(GoodsOutDO::getTakeoutReason, GoodsTakeoutReasonEnum.LSQWKQ.getCode()))
                .isNull(GoodsOutDO::getApprovalResult);
        Page<GoodsOutDO> goodsOutPage = this.page(page, wrapper);
        List<GoodsOutDO> records = goodsOutPage.getRecords();
        if (!CollectionUtils.isEmpty(records)) {
            List<String> goodsOutIdList = records.stream().map(GoodsOutDO::getId).collect(Collectors.toList());

            List<GoodsOutDetailDO> goodsOutDetailDOList =
                    goodsOutDetailService.lambdaQuery().in(GoodsOutDetailDO::getPersonalGoodsOutId, goodsOutIdList).list();
            List<String> wpbhList = goodsOutDetailDOList.stream().map(GoodsOutDetailDO::getWpbh).collect(Collectors.toList());
            List<GoodsDetailDO> goodsDetailDOList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(wpbhList)) {
                goodsDetailDOList = goodsDetailService.lambdaQuery().in(GoodsDetailDO::getWpbh, wpbhList).list();
            }
            List<GoodsDetailDO> finalGoodsDetailDOList = goodsDetailDOList;
            List<GoodsOutApprovalRespVO> approvalRespVOList = records.stream().map(goodsOut -> {
                GoodsOutApprovalRespVO respVO = BeanUtils.toBean(goodsOut, GoodsOutApprovalRespVO.class);

                //获取物品取出详情
                List<GoodsOutDetailRespVO> goodOutDetailRespVOList = goodsOutDetailDOList.stream().filter(goodsOutDetail ->
                        goodsOutDetail.getPersonalGoodsOutId().equals(goodsOut.getId())).map(goodsOutDetail -> {
                    GoodsOutDetailRespVO goodsOutDetailRespVO = BeanUtils.toBean(goodsOutDetail, GoodsOutDetailRespVO.class);
                    GoodsDetailDO goodsDetailDO = finalGoodsDetailDOList.stream()
                            .filter(goodsDetail -> goodsOutDetail.getWpbh().equals(goodsDetail.getWpbh())).findFirst().orElse(null);
                    goodsOutDetailRespVO.setName(goodsDetailDO.getName());
                    goodsOutDetailRespVO.setUnit(goodsDetailDO.getUnit());
                    return goodsOutDetailRespVO;
                }).collect(Collectors.toList());


                Map<String, List<GoodsOutDetailRespVO>> group = goodOutDetailRespVOList.stream().collect(Collectors.groupingBy(GoodsOutDetailRespVO::getName));
                String goodsInfo = group.keySet().stream().map(name -> {
                    List<GoodsOutDetailRespVO> goodsOutDetailRespVOS = group.get(name);
                    //数量
                    int sum = goodsOutDetailRespVOS.stream().mapToInt(GoodsOutDetailRespVO::getTakeoutQuantity).sum();
                    String unit = goodsOutDetailRespVOS.stream().map(GoodsOutDetailRespVO::getUnit).findFirst().orElse(null);
                    //名称
                    String nameDic = DicUtils.translate("ZD_FWGLWPZL", name);
                    //单位
                    String unitDic = DicUtils.translate("ZD_WPDW", unit);
                    return nameDic + sum + unitDic;
                }).collect(Collectors.joining(","));
                respVO.setGoodsInfo(goodsInfo);
                return respVO;
            }).collect(Collectors.toList());
            return new PageResult<>(approvalRespVOList, goodsOutPage.getTotal());
        }
        return PageResult.empty();
    }


}
