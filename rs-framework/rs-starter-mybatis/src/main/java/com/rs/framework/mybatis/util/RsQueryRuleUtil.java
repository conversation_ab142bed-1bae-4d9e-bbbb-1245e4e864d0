package com.rs.framework.mybatis.util;

import cn.hutool.db.Db;
import com.alibaba.fastjson.TypeReference;
import com.bsp.common.cache.RedisClient;
import com.bsp.common.cache.RedisEnum;
import com.bsp.common.cache.RedisUtils;
import com.bsp.common.util.StringUtil;
import com.bsp.common.util.TemplateUtils;
import com.bsp.sdk.perm.model.QueryRule;
import com.rs.framework.common.exception.ServiceException;
import lombok.extern.log4j.Log4j2;
import org.springframework.util.CollectionUtils;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @ClassName RsQueryRuleUtil
 * <AUTHOR>
 * @Date 2025/5/9 18:33
 * @Version 1.0
 */
@Log4j2
public class RsQueryRuleUtil {

    public static String getRuleSql(String mark, String roleIds, String roleGroupIds) {
        String sql = "";
        List<QueryRule> list = (List) RedisClient.get(RedisUtils.buildKey(RedisEnum.COM_GRID, mark + ":queryRule"), new TypeReference<List<QueryRule>>() {});
        if (CollectionUtils.isEmpty(list)) {
            try {
                String querySql = String.format("select * from com_model_query_rule where model_mark = '%s' order by level asc", mark);
                Db use = UacUserUtil.getDb();
                try {
                    list = use.query(querySql,QueryRule.class);
                } catch (SQLException e) {
                    log.error("查询权限规则失败", e);
                }
                RedisClient.set(RedisUtils.buildKey(RedisEnum.COM_GRID,mark + ":queryRule"), list);
            } catch (Exception e) {

            }
        }

        List<QueryRule> rules = new ArrayList();
        QueryRule defaultRule = null;
        if (!CollectionUtils.isEmpty(list)) {
            if (StringUtil.isNotEmpty(roleIds) && StringUtil.isNotEmpty(roleGroupIds)) {
                roleIds = roleIds + "," + roleGroupIds;
            } else if (StringUtil.isEmpty(roleIds) && StringUtil.isNotEmpty(roleGroupIds)) {
                roleIds = roleGroupIds;
            }

            int level = -99;

            int i;
            QueryRule item;
            for(i = 0; i < list.size(); ++i) {
                item = (QueryRule)list.get(i);
                if ("1".equals(item.getIsDefault())) {
                    defaultRule = item;
                } else if (StringUtil.isNotEmpty(roleIds) && StringUtil.isNotEmpty(item.getRoleId())) {
                    String[] roleIdArr = roleIds.split(",");
                    String[] var11 = roleIdArr;
                    int var12 = roleIdArr.length;

                    for(int var13 = 0; var13 < var12; ++var13) {
                        String roleId = var11[var13];
                        if (item.getRoleId().indexOf(roleId) > -1 && !rules.contains(item)) {
                            if (item.getLevel() > level) {
                                level = item.getLevel();
                                rules = new ArrayList();
                            }

                            rules.add(item);
                        }
                    }
                }
            }

            if (!CollectionUtils.isEmpty(rules)) {
                for(i = 0; i < rules.size(); ++i) {
                    item = (QueryRule)rules.get(i);
                    if (i != 0) {
                        sql = sql + " or ";
                    }

                    sql = sql + item.getScript();
                }

                if (null != defaultRule) {
                    if (defaultRule.getLevel() > level) {
                        sql = defaultRule.getScript();
                    } else if (defaultRule.getLevel() == level) {
                        if (StringUtil.isNotEmpty(sql)) {
                            sql = sql + " or ";
                        }

                        sql = sql + defaultRule.getScript();
                    }
                }
            } else if (null != defaultRule) {
                sql = StringUtil.isEmpty(defaultRule.getScript()) ? "" : defaultRule.getScript();
            }
        }

        return sql;
    }

    public static String getRuleSql(String mark, String roleIds) {
        return getRuleSql(mark, roleIds, (String)null);
    }

    public static String formatRuleSql(String sql, Object sessionUser, Map<String, Object> extendParas) {
        if (sessionUser == null) {
            throw new ServiceException("SessionUser不能为空！");
        } else {
            return extendParas == null ? TemplateUtils.parseTemplate(sql, sessionUser) : TemplateUtils.parseTemplate(sql, extendParas, sessionUser);
        }
    }

    public static String formatRuleSql(String sql, Object sessionUser) {
        return formatRuleSql(sql, sessionUser, (Map)null);
    }
}
